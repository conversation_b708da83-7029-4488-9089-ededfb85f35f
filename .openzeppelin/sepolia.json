{"manifestVersion": "3.2", "proxies": [{"address": "0xb3E14926cdC045A8E7769053669189173cF3292a", "txHash": "0x3f31eaf2739990cb5f74d7c31e57d08d4724decf41ded6bb48434678e8367183", "kind": "uups"}], "impls": {"a6209cb7654fc578ec411b3285a0d134a513cae7c9e6992b65215b7eb68c60d5": {"address": "0x884B39dAC862e04c9FBeB7C2015788cB676A0656", "txHash": "0xfe0549496036c9d12eecd5b80da00470625224b191ee562f17068c5a28b3537c", "layout": {"solcVersion": "0.8.28", "storage": [{"label": "startBlock", "offset": 0, "slot": "0", "type": "t_uint256", "contract": "ZenStake", "src": "contracts/ZenStake.sol:54"}, {"label": "endBlock", "offset": 0, "slot": "1", "type": "t_uint256", "contract": "ZenStake", "src": "contracts/ZenStake.sol:55"}, {"label": "ZenPerBlock", "offset": 0, "slot": "2", "type": "t_uint256", "contract": "ZenStake", "src": "contracts/ZenStake.sol:56"}, {"label": "withdrawPaused", "offset": 0, "slot": "3", "type": "t_bool", "contract": "ZenStake", "src": "contracts/ZenStake.sol:58"}, {"label": "claimPaused", "offset": 1, "slot": "3", "type": "t_bool", "contract": "ZenStake", "src": "contracts/ZenStake.sol:60"}, {"label": "Zen", "offset": 2, "slot": "3", "type": "t_contract(IERC20)2469", "contract": "ZenStake", "src": "contracts/ZenStake.sol:60"}, {"label": "totalPoolWeight", "offset": 0, "slot": "4", "type": "t_uint256", "contract": "ZenStake", "src": "contracts/ZenStake.sol:63"}, {"label": "pool", "offset": 0, "slot": "5", "type": "t_array(t_struct(Pool)9892_storage)dyn_storage", "contract": "ZenStake", "src": "contracts/ZenStake.sol:65"}, {"label": "user", "offset": 0, "slot": "6", "type": "t_mapping(t_uint256,t_mapping(t_address,t_struct(User)9903_storage))", "contract": "ZenStake", "src": "contracts/ZenStake.sol:67"}], "types": {"t_address": {"label": "address", "numberOfBytes": "20"}, "t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_bytes32": {"label": "bytes32", "numberOfBytes": "32"}, "t_mapping(t_address,t_bool)": {"label": "mapping(address => bool)", "numberOfBytes": "32"}, "t_mapping(t_bytes32,t_struct(RoleData)24_storage)": {"label": "mapping(bytes32 => struct AccessControlUpgradeable.RoleData)", "numberOfBytes": "32"}, "t_struct(AccessControlStorage)34_storage": {"label": "struct AccessControlUpgradeable.AccessControlStorage", "members": [{"label": "_roles", "type": "t_mapping(t_bytes32,t_struct(RoleData)24_storage)", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(InitializableStorage)145_storage": {"label": "struct Initializable.InitializableStorage", "members": [{"label": "_initialized", "type": "t_uint64", "offset": 0, "slot": "0"}, {"label": "_initializing", "type": "t_bool", "offset": 8, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(PausableStorage)296_storage": {"label": "struct PausableUpgradeable.PausableStorage", "members": [{"label": "_paused", "type": "t_bool", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(RoleData)24_storage": {"label": "struct AccessControlUpgradeable.RoleData", "members": [{"label": "hasRole", "type": "t_mapping(t_address,t_bool)", "offset": 0, "slot": "0"}, {"label": "adminRole", "type": "t_bytes32", "offset": 0, "slot": "1"}], "numberOfBytes": "64"}, "t_uint64": {"label": "uint64", "numberOfBytes": "8"}, "t_array(t_struct(Pool)9892_storage)dyn_storage": {"label": "struct ZenStake.Pool[]", "numberOfBytes": "32"}, "t_array(t_struct(UnstakeRequest)9908_storage)dyn_storage": {"label": "struct ZenStake.UnstakeRequest[]", "numberOfBytes": "32"}, "t_contract(IERC20)2469": {"label": "contract IERC20", "numberOfBytes": "20"}, "t_mapping(t_address,t_struct(User)9903_storage)": {"label": "mapping(address => struct ZenStake.User)", "numberOfBytes": "32"}, "t_mapping(t_uint256,t_mapping(t_address,t_struct(User)9903_storage))": {"label": "mapping(uint256 => mapping(address => struct ZenStake.User))", "numberOfBytes": "32"}, "t_struct(Pool)9892_storage": {"label": "struct ZenStake.Pool", "members": [{"label": "stTokenAddress", "type": "t_address", "offset": 0, "slot": "0"}, {"label": "poolWeight", "type": "t_uint256", "offset": 0, "slot": "1"}, {"label": "lastRewardBlock", "type": "t_uint256", "offset": 0, "slot": "2"}, {"label": "accZenPerST", "type": "t_uint256", "offset": 0, "slot": "3"}, {"label": "stTokenAmount", "type": "t_uint256", "offset": 0, "slot": "4"}, {"label": "minDepositAmount", "type": "t_uint256", "offset": 0, "slot": "5"}, {"label": "unstakeLockedBlocks", "type": "t_uint256", "offset": 0, "slot": "6"}], "numberOfBytes": "224"}, "t_struct(UnstakeRequest)9908_storage": {"label": "struct ZenStake.UnstakeRequest", "members": [{"label": "amount", "type": "t_uint256", "offset": 0, "slot": "0"}, {"label": "unlockBlocks", "type": "t_uint256", "offset": 0, "slot": "1"}], "numberOfBytes": "64"}, "t_struct(User)9903_storage": {"label": "struct ZenStake.User", "members": [{"label": "stAmount", "type": "t_uint256", "offset": 0, "slot": "0"}, {"label": "finishedZen", "type": "t_uint256", "offset": 0, "slot": "1"}, {"label": "pendingZen", "type": "t_uint256", "offset": 0, "slot": "2"}, {"label": "requests", "type": "t_array(t_struct(UnstakeRequest)9908_storage)dyn_storage", "offset": 0, "slot": "3"}], "numberOfBytes": "128"}, "t_uint256": {"label": "uint256", "numberOfBytes": "32"}}, "namespaces": {"erc7201:openzeppelin.storage.Pausable": [{"contract": "PausableUpgradeable", "label": "_paused", "type": "t_bool", "src": "@openzeppelin/contracts-upgradeable/utils/PausableUpgradeable.sol:21", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.AccessControl": [{"contract": "AccessControlUpgradeable", "label": "_roles", "type": "t_mapping(t_bytes32,t_struct(RoleData)24_storage)", "src": "@openzeppelin/contracts-upgradeable/access/AccessControlUpgradeable.sol:61", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.Initializable": [{"contract": "Initializable", "label": "_initialized", "type": "t_uint64", "src": "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:69", "offset": 0, "slot": "0"}, {"contract": "Initializable", "label": "_initializing", "type": "t_bool", "src": "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:73", "offset": 8, "slot": "0"}]}}}}}