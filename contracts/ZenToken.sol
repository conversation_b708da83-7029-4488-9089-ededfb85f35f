// SPDX-License-Identifier: MIT
pragma solidity ^0.8.20;

import "@openzeppelin/contracts/token/ERC20/ERC20.sol";
import "@openzeppelin/contracts/token/ERC20/extensions/ERC20Burnable.sol";
import "@openzeppelin/contracts/token/ERC20/extensions/ERC20Pausable.sol";
import "@openzeppelin/contracts/access/Ownable.sol";
import "@openzeppelin/contracts/token/ERC20/extensions/ERC20Permit.sol";

/**
 * @title ZenToken
 * @dev ZEN代币合约 - 用作质押奖励代币
 * 
 * 特性:
 * - 标准ERC20功能
 * - 可燃烧 (Burnable)
 * - 可暂停 (Pausable) 
 * - 所有者权限管理
 * - EIP-2612 许可功能 (Permit)
 * - 固定总供应量
 */
contract ZenToken is ERC20, ERC20Burnable, ERC20Pausable, Ownable, ERC20Permit {
    
    // 代币基本信息
    uint256 public constant TOTAL_SUPPLY = 1_000_000_000 * 10**18; // 10亿代币
    uint256 public constant DECIMALS = 18;
    
    // 事件
    event TokensMinted(address indexed to, uint256 amount);
    event TokensBurned(address indexed from, uint256 amount);
    
    /**
     * @dev 构造函数
     * @param initialOwner 初始所有者地址
     */
    constructor(address initialOwner) 
        ERC20("ZEN Token", "ZEN") 
        Ownable(initialOwner)
        ERC20Permit("ZEN Token")
    {
        // 将所有代币铸造给初始所有者
        _mint(initialOwner, TOTAL_SUPPLY);
        emit TokensMinted(initialOwner, TOTAL_SUPPLY);
    }
    
    /**
     * @dev 返回代币小数位数
     */
    function decimals() public pure override returns (uint8) {
        return 18;
    }
    
    /**
     * @dev 铸造代币 (仅所有者)
     * @param to 接收地址
     * @param amount 铸造数量
     */
    function mint(address to, uint256 amount) public onlyOwner {
        _mint(to, amount);
        emit TokensMinted(to, amount);
    }
    
    /**
     * @dev 暂停代币转账 (仅所有者)
     */
    function pause() public onlyOwner {
        _pause();
    }
    
    /**
     * @dev 恢复代币转账 (仅所有者)
     */
    function unpause() public onlyOwner {
        _unpause();
    }
    
    /**
     * @dev 批量转账
     * @param recipients 接收者地址数组
     * @param amounts 转账金额数组
     */
    function batchTransfer(address[] calldata recipients, uint256[] calldata amounts) 
        external 
        returns (bool) 
    {
        require(recipients.length == amounts.length, "ZenToken: arrays length mismatch");
        require(recipients.length > 0, "ZenToken: empty arrays");
        
        for (uint256 i = 0; i < recipients.length; i++) {
            require(recipients[i] != address(0), "ZenToken: transfer to zero address");
            require(amounts[i] > 0, "ZenToken: transfer amount must be greater than zero");
            _transfer(msg.sender, recipients[i], amounts[i]);
        }
        
        return true;
    }
    
    /**
     * @dev 紧急提取误发送的代币 (仅所有者)
     * @param token 代币合约地址
     * @param to 接收地址
     * @param amount 提取数量
     */
    function emergencyWithdraw(address token, address to, uint256 amount) 
        external 
        onlyOwner 
    {
        require(token != address(this), "ZenToken: cannot withdraw ZEN tokens");
        require(to != address(0), "ZenToken: withdraw to zero address");
        
        IERC20(token).transfer(to, amount);
    }
    
    /**
     * @dev 重写 _update 函数以支持暂停功能
     */
    function _update(address from, address to, uint256 value)
        internal
        override(ERC20, ERC20Pausable)
    {
        super._update(from, to, value);
    }
    
    /**
     * @dev 重写 burn 函数以添加事件
     */
    function burn(uint256 amount) public override {
        super.burn(amount);
        emit TokensBurned(msg.sender, amount);
    }
    
    /**
     * @dev 重写 burnFrom 函数以添加事件
     */
    function burnFrom(address account, uint256 amount) public override {
        super.burnFrom(account, amount);
        emit TokensBurned(account, amount);
    }
}
