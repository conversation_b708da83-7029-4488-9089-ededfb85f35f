// SPDX-License-Identifier: SEE LICENSE IN LICENSE
pragma solidity ^0.8.20;

import "@openzeppelin/contracts/token/ERC20/IERC20.sol";
import "@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol";
import "@openzeppelin/contracts/utils/Address.sol";
import "@openzeppelin/contracts/utils/math/Math.sol";

import "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol";
import "@openzeppelin/contracts-upgradeable/proxy/utils/UUPSUpgradeable.sol";
import "@openzeppelin/contracts-upgradeable/access/AccessControlUpgradeable.sol";
import "@openzeppelin/contracts-upgradeable/utils/PausableUpgradeable.sol";

abstract contract ZenStake is
    Initializable,
    UUPSUpgradeable,
    AccessControlUpgradeable,
    PausableUpgradeable
{
    using SafeERC20 for IERC20;
    using Address for address;
    using Math for uint256;

    // constant 常量
    bytes32 public constant ADMIN_ROLE = keccak256("admin_role");
    bytes32 public constant UPGRADE_ROLE = keccak256("upgrade_role");
    uint256 public constant ETH_PID = 0;

    // structure 定义结构体
    struct Pool {
        address stTokenAddress;       // 质押代币地址
        uint256 poolWeight;           // 池权重
        uint256 lastRewardBlock;      // 最后奖励区块
        uint256 accZenPerST;          // 累计每代币奖励
        uint256 stTokenAmount;        // 总质押量
        uint256 minDepositAmount;     // 最小质押量
        uint256 unstakeLockedBlocks;  // 解锁区块数
    }
    struct User {
        uint256 stAmount;           // 用户质押数量
        uint256 finishedZen;        // 已分配奖励
        uint256 pendingZen;         // 待领取奖励
        UnstakeRequest[] requests;  // 解质请求列表
    }
    struct UnstakeRequest {
        uint256 amount;         // 解质押金额
        uint256 unlockBlocks;   // 解锁区块号
    }

    // state variables 变量
    uint256 public startBlock;      // 质押开始区块
    uint256 public endBlock;        // 质押结束区块
    uint256 public ZenPerBlock;     // 每区块的奖励
    bool public withdrawPaused;     // 暂停提取功能
    bool public claimPaused;        // 暂停领取奖励功能
    IERC20 public Zen;              // 奖励代币合约
    uint256 public totalPoolWeight; // 所有池的总权重
    Pool[] public pool;             // 池数组

    mapping (uint256 => mapping (address => User)) public user; // pool id => user.address => user info

    // event 事件
    event SetZen(IERC20 indexed Zen); 
    event PauseWithdraw();
    event UnpauseWithdraw();
    event PauseClaim();
    event UnpauseClaim();
    event SetStartBlock(uint256 indexed startBlock);
    event SetEndBlock(uint256 indexed endBlock);
    event SetZenPerBlock(uint256 indexed ZenPerBlock);
    event AddPool(address indexed stTokenAddress, uint256 indexed poolWeight, uint256 indexed lastRewardBlock, uint256 minDepositAmount, uint256 unstakeLockBlocks);
    event UpdatePool(uint256 indexed poolId, uint256 indexed lastRewardBlock, uint256 totalZen);
    event UpdatePoolInfo(uint256 indexed poolId, uint256 indexed minDepositAmount, uint256 unstakeLockedBlocks);
    event SetPoolWeight(uint256 indexed poolId, uint256 indexed poolWeight, uint256 indexed totalPoolWeight);
    event Deposit(address indexed user, uint256 indexed poolId, uint256 amount);
    event RequestUnstake(address indexed user, uint256 indexed poolId, uint256 amount);
    event Withdraw (address indexed user, uint256 indexed poolId, uint256 amount, uint256 indexed blockNumber);
    event Claim(address indexed user, uint256 indexed poolId, uint256 ZenReward);

    // modifier 
    modifier checkPid(uint256 _pid) {
      require(_pid < pool.length, 'invalid pid');
      _;
    }
    modifier whenNotClaimPaused() {
      require(!claimPaused, 'claim is paused');
      _;
    }
    modifier whenNotWithdrawPaused() {
      require(!withdrawPaused, 'withdraw is paused');
      _;
    }

    function initialize(
      IERC20 _Zen,
      uint256 _startBlock,
      uint256 _endBlock,
      uint256 _ZenPerBlock
    ) public initializer {
        // 参数校验
        require(_startBlock <= _endBlock && _ZenPerBlock > 0, "invalid parameters");
        // 权限系统初始化
        __AccessControl_init(); 
        // 可升级功能
        __UUPSUpgradeable_init();
        // 分配管理员权限
        _grantRole(DEFAULT_ADMIN_ROLE, msg.sender); // DEFAULT_ADMIN_ROLE 继承而来. AccessControlUpgradeable -> AccessControl
        _grantRole(ADMIN_ROLE, msg.sender);
        _grantRole(UPGRADE_ROLE, msg.sender);

        setZen(_Zen);

        startBlock = _startBlock;
        endBlock = _endBlock;
        ZenPerBlock = _ZenPerBlock;
    }

    function _authorizeUpgrade(address newImplementation) internal onlyRole(UPGRADE_ROLE) override {}

    function setZen(IERC20 _Zen) public onlyRole(ADMIN_ROLE) {
      Zen = _Zen;
      emit SetZen(Zen);
    }

    function pauseWithdraw() public onlyRole(ADMIN_ROLE) {
      require(!withdrawPaused, "withdraw has been already paused");

      withdrawPaused = true;

      emit PauseWithdraw();
    }

    function unpauseWithdraw() public onlyRole(ADMIN_ROLE) {
      require(withdrawPaused, "withdraw has been already unpaused");

      withdrawPaused = false;

      emit UnpauseWithdraw();
    }

    function pauseClaim() public onlyRole(ADMIN_ROLE) {
      require(!claimPaused, "claim has been already paused");

      claimPaused = true;

      emit PauseClaim();
    }

    function unpauseClaim() public onlyRole(ADMIN_ROLE) {
      require(claimPaused, "claim has been already unpaused");

      claimPaused = false;

      emit UnpauseClaim();
    }

    function setStartBlock(uint256 _startBlock) public onlyRole(ADMIN_ROLE) {
      require(_startBlock < endBlock, 'start block must be smaller than end block');

      startBlock = _startBlock;

      emit SetStartBlock(startBlock);
    }

    function setEndBlock(uint256 _endBlock) public onlyRole(ADMIN_ROLE) {
      require(_endBlock > startBlock , 'start block must be smaller than end block');

      endBlock = _endBlock;

      emit SetEndBlock(endBlock);
    }

    function setZenPerBlock(uint256 _ZenPerBlock) public onlyRole(ADMIN_ROLE) {
      require(_ZenPerBlock > 0 , 'invalid parameter');

      ZenPerBlock = _ZenPerBlock;

      emit SetZenPerBlock(_ZenPerBlock);
    }

    function addPool(address _stTokenAddress, uint256 _poolWeight, uint256 _minDepositAmount, uint256 _unstakeLockedBlocks, bool _withUpdate) public onlyRole(ADMIN_ROLE) {
      if (pool.length > 0) {
        require(_stTokenAddress != address(0x0), 'invalid stTokenAddress');
      } else {
        require(_stTokenAddress == address(0x0), 'invalid stTokenAddress');
      }

      // 锁定期一定要求大于0
      require(_unstakeLockedBlocks > 0, 'invalid withdraw locked blocks');
      // 判断挖矿是否结束
      require(block.number < endBlock, 'Already ended');

      if (_withUpdate) {
        massUpdatePools();
      }

      // 最后奖励区块
      uint256 lastRewardBlock = block.number > startBlock ? block.number : startBlock;
      // 计算总权重
      totalPoolWeight = totalPoolWeight + _poolWeight;

      pool.push(Pool({
        stTokenAddress: _stTokenAddress,
        poolWeight: _poolWeight,
        lastRewardBlock: lastRewardBlock,
        accZenPerST: 0,
        stTokenAmount: 0,
        minDepositAmount: _minDepositAmount,
        unstakeLockedBlocks: _unstakeLockedBlocks
      }));

      emit AddPool(_stTokenAddress, _poolWeight, lastRewardBlock, _minDepositAmount, _unstakeLockedBlocks);
    }

    function massUpdatePools() public {
      uint256 length = pool.length;
      for (uint256 pid = 0; pid < length; pid++) {
        updatePool(pid);
      }
    }

    function updatePool(uint256 _pid) public checkPid(_pid) {
      Pool storage pool_ = pool[_pid];

      if(block.number <= pool_.lastRewardBlock) return;

      // 计算总奖励
      (bool success1, uint256 totalZen) = getMultiplier(pool_.lastRewardBlock, block.number).tryMul(pool_.poolWeight);
      require(success1, 'overflow');

      // 分配权重
      (success1, totalZen) = totalZen.tryDiv(totalPoolWeight);
      require(success1, 'overflow');

      // 该池的总质押
      uint256 stSupply = pool_.stTokenAmount;
      if (stSupply > 0) {
        // 单位换算
        (bool success2, uint256 totalZen_) = totalZen.tryMul(1 ether);
        require(success2, 'overflow');

        // 区块的奖励计算
        (success2, totalZen_) = totalZen_.tryDiv(stSupply);
        require(success2, 'overflow');

        // 增加池中累加奖励
        (bool success3, uint256 accZenPerST) = pool_.accZenPerST.tryAdd(totalZen);
        require(success3, 'overflow');

        pool_.accZenPerST = accZenPerST;
      }

      pool_.lastRewardBlock = block.number;

      emit UpdatePool(_pid, pool_.lastRewardBlock, totalZen);
    }

    function updatePool(uint256 _pid, uint256 _minDepositAmount, uint256 _unstakeLockedBlocks) public onlyRole(ADMIN_ROLE) checkPid(_pid) {
      pool[_pid].minDepositAmount = _minDepositAmount;
      pool[_pid].unstakeLockedBlocks = _unstakeLockedBlocks;

      emit UpdatePoolInfo(_pid, _minDepositAmount, _unstakeLockedBlocks);
    }

    function setPoolWeight(uint256 _pid, uint256 _poolWeight, bool _withUpdate) public onlyRole(ADMIN_ROLE) checkPid(_pid) {
      require(_poolWeight > 0, 'invalid pool weight');

      if(_withUpdate) {
        massUpdatePools();
      }

      totalPoolWeight = totalPoolWeight - pool[_pid].poolWeight + _poolWeight;
      pool[_pid].poolWeight = _poolWeight;

      emit SetPoolWeight(_pid, _poolWeight, totalPoolWeight);
    }

    function pendingZen(uint256 _pid, address _user) external checkPid(_pid) view returns(uint256) {
      return pendingZenByBlockNumber(_pid, _user, block.number);
    }

    // 指定区块的待领取的奖励
    function pendingZenByBlockNumber(uint256 _pid, address _user, uint256 _blockNumber) public view checkPid(_pid)  returns (uint256) {
      Pool storage pool_ = pool[_pid];
      User storage user_ = user[_pid][_user];
      uint256 accZenPerST = pool_.accZenPerST;
      uint256 stSupply = pool_.stTokenAmount;

      if(_blockNumber > pool_.lastRewardBlock && stSupply != 0) {
        uint256 multiplier = getMultiplier(pool_.lastRewardBlock, _blockNumber);
        uint256 ZenPerForPool = multiplier * pool_.poolWeight / totalPoolWeight;
        accZenPerST = accZenPerST + ZenPerForPool * (1 ether) / stSupply;
      }

      return user_.stAmount * accZenPerST / (1 ether) - user_.finishedZen + user_.pendingZen;
    }

    function withdrawAmount(uint256 _pid, address _user) public checkPid(_pid) view returns(uint256 requestAmount, uint256 pendingWithdrawAmount) {
      User storage user_ = user[_pid][_user];
      
      for (uint256 i = 0; i < user_.requests.length; i++) {
        if(user_.requests[i].unlockBlocks <= block.number) {
          pendingWithdrawAmount = pendingWithdrawAmount + user_.requests[i].amount;
        }
        requestAmount = requestAmount + user_.requests[i].amount;
      }
    }

    function getMultiplier(uint256 _from, uint256 _to) public view returns (uint256 multiplier) {
      require(_from < _to, 'invalid block');
      if(_from < startBlock) {
        _from = startBlock;
      }
      if(_to > endBlock) {
        _to = endBlock;
      }
      require(_from < _to, 'end block must be greater than start block');

      bool success;
      (success, multiplier) = (_to - _from).tryMul(ZenPerBlock);
      require(success, 'multiplier overflow');
    }

    function poolLength() external view returns(uint256) {
      return pool.length;
    }

    function stakingBalance(uint256 _pid, address _user) external checkPid(_pid) view returns (uint256) {
      return user[_pid][_user].stAmount;
    } 

    function depositETH() public whenNotPaused() payable {
      Pool storage pool_ = pool[ETH_PID];
      require(pool_.stTokenAddress == address(0x0), 'invalid staking token address');

      uint256 _amount = msg.value;
      require(_amount >= pool_.minDepositAmount, 'deposit amount is too small');

      _deposit(ETH_PID, _amount);
    }

    function _deposit(uint256 _pid, uint256 _amount) internal {
      Pool storage pool_ = pool[_pid];
      User storage user_ = user[_pid][msg.sender];

      updatePool(_pid);

      // 用户存在质押
      if(user_.stAmount > 0) {
        // 计算总奖励
        (bool success1, uint256 accST) = user_.stAmount.tryMul(pool_.accZenPerST);
        require(success1, 'user stAmount mul accZenPerST overflow');
        // 单位换算
        (success1, accST) = accST.tryDiv(1 ether);
        require(success1, 'accST div 1 ether overflow');
        // 减去已经分配的奖励 
        (bool success2, uint256 pendingZen_) = accST.trySub(user_.finishedZen);
        require(success2, 'accST sub finishedZen overflow"');
        // 存在奖励 更新用户奖励
        if (pendingZen_ > 0) {
          (bool success3, uint256 _pendingZen) = user_.pendingZen.tryAdd(pendingZen_);
          require(success3, 'user pendingZen overflow');
          user_.pendingZen = _pendingZen;
        }
      }
      // 更新用户质押代币数量
      if (_amount > 0) {
        (bool success4, uint256 stAmount_) = user_.stAmount.tryAdd(_amount);
        require(success4, 'user stAmount overflow');
        user_.stAmount = stAmount_; 
      }
      // 更新池中的总质押
      (bool success5, uint256 stTokenAmount_) = pool_.stTokenAmount.tryAdd(_amount);
      require(success5, 'pool stTokenAmount overflow');
      pool_.stTokenAmount = stTokenAmount_;
      // 更新用户计算奖励的结算基准点
      (bool success6, uint256 finishedZen_) = user_.stAmount.tryMul(pool_.accZenPerST);
      require(success6, 'user stAmount mul accZenPerST overflow');

      (success6, finishedZen_) = finishedZen_.tryDiv(1 ether);
      require(success6, 'finishedZen div 1 ether overflow');

      user_.finishedZen = finishedZen_;

      emit Deposit(msg.sender, _pid, _amount);
    }

    function unstake(uint256 _pid, uint256 _amount) public whenNotPaused() checkPid(_pid) whenNotWithdrawPaused() {
      Pool storage pool_ = pool[_pid];
      User storage user_ = user[_pid][msg.sender];

      require(user_.stAmount > _amount, 'Not enough staking token balance');

      updatePool(_pid);

      uint256 pendingZen_ = user_.stAmount * pool_.accZenPerST / (1 ether) - user_.finishedZen;

      if(pendingZen_ > 0) {
        user_.pendingZen = user_.pendingZen + pendingZen_;
      }

      if(_amount > 0) {
        user_.stAmount = user_.stAmount - _amount;
        user_.requests.push(UnstakeRequest({
          amount: _amount,
          unlockBlocks: block.number + pool_.unstakeLockedBlocks
        }));
      }

      pool_.stTokenAmount = pool_.stTokenAmount - _amount;
      user_.finishedZen = user_.stAmount * pool_.accZenPerST / (1 ether);

      emit RequestUnstake(msg.sender, _pid, _amount);
    }

    function withdraw(uint256 _pid) public whenNotPaused() whenNotWithdrawPaused() checkPid(_pid) {
      Pool storage pool_ = pool[_pid];
      User storage user_ = user[_pid][msg.sender];

      uint256 pendingWithdraw_;
      uint256 popNum_;
      for (uint256 i = 0; i < user_.requests.length; i++) {
        if(user_.requests[i].unlockBlocks > block.number) {
          break;
        }

        pendingWithdraw_ = pendingWithdraw_ + user_.requests[i].amount;
        popNum_++;
      }

      for (uint256 i = 0; i < user_.requests.length - popNum_; i++) {
        user_.requests[i] = user_.requests[i + popNum_];
      }

      for (uint256 i = 0; i < popNum_; i++) {
        user_.requests.pop();
      }

      if(pendingWithdraw_ > 0) {
        if(pool_.stTokenAddress == address(0x0)) {
          _safeETHTransfer(msg.sender, pendingWithdraw_);
        } else {
          IERC20(pool_.stTokenAddress).safeTransfer(msg.sender, pendingWithdraw_);
        }
      }

      emit Withdraw(msg.sender, _pid, pendingWithdraw_, block.number);
    }

    function claim(uint256 _pid) public whenNotPaused() whenNotClaimPaused() checkPid(_pid) {
      Pool storage pool_ = pool[_pid];
      User storage user_ = user[_pid][msg.sender];

      updatePool(_pid);

      uint256 pendingZen_ = user_.stAmount * pool_.accZenPerST / (1 ether) - user_.finishedZen + user_.pendingZen;

      if(pendingZen_ > 0) {
        user_.pendingZen = 0;
        _safeZenTransfer(msg.sender, pendingZen_);
      }

      user_.finishedZen = user_.stAmount * pool_.accZenPerST / (1 ether);

      emit Claim(msg.sender, _pid, pendingZen_);
    }


    function _safeZenTransfer(address _to, uint256 _amount) internal {
      uint256 ZenBal = Zen.balanceOf(address(this));

      if(_amount > ZenBal) {
        Zen.transfer(_to, ZenBal);
      } else {
        Zen.transfer(_to, _amount);
      }
    }

    function _safeETHTransfer(address _to, uint256 _amount) internal {
      (bool success, bytes memory data) = address(_to).call{value: _amount}("");

      require(success, 'ETH transfer call failed');

      if(data.length > 0) {
        require(abi.decode(data, (bool)), 'ETH transfer operation did not succeed');
      }
    }
}
