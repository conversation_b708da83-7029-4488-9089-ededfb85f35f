{"name": "zen-contract-stake", "version": "1.0.0", "main": "index.js", "scripts": {"test": "npx hardhat test", "compile": "npx hardhat compile", "deploy:sepolia": "npx hardhat run scripts/deploy.ts --network sepolia", "deploy:zen-token": "npx hardhat run scripts/deploy-zen-token.ts --network sepolia", "deploy:zen-token:local": "npx hardhat run scripts/deploy-zen-token.ts --network localhost", "node": "npx hardhat node"}, "keywords": [], "author": "", "license": "ISC", "description": "", "devDependencies": {"@nomicfoundation/hardhat-toolbox": "^6.0.0", "hardhat": "^2.25.0"}, "dependencies": {"@openzeppelin/contracts": "^5.3.0", "@openzeppelin/contracts-upgradeable": "^5.3.0", "@openzeppelin/hardhat-upgrades": "^3.9.1"}}