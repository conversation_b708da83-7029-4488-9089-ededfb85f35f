import { expect } from "chai";
import { ethers } from "hardhat";
import { loadFixture } from "@nomicfoundation/hardhat-toolbox/network-helpers";

describe('ZenStake', () => {
  // 部署 ERC20 代币
  async function deployERC20() {
    const [deployer] = await ethers.getSigners();
    const token = await ethers.getContractFactory("MockERC20")
    const mockToken = await token.deploy("Mock Zen", "ZEN")

    return { mockToken, deployer }
  }
  // 部署 ZenStake
  async function deployZenStakeFixture() {
    const [owner, user1, user2] = await ethers.getSigners()

    // 部署 ERC20
    const { mockToken } = await deployERC20()

    // 部署 ZenStake 合约
    const ZenStakeToken = await ethers.getContractFactory('ZenStakeImpl')
    const zenStake = await ZenStakeToken.deploy()

    // 设置合约参数
    const currentBlock = await ethers.provider.getBlockNumber()
    const startBlock = currentBlock + 10;
    const endBlock = currentBlock + 1000;
    const zenPerBlock = ethers.parseEther('1')

    return {
      owner,
      user1,
      user2,
      mockToken,
      zenStake,
      startBlock,
      endBlock,
      zenPerBlock
    }
  }

  describe('initial', () => {
    it('parameters should be initialized correctly', async () => {
      const { zenStake, mockToken, startBlock, endBlock, zenPerBlock } =
        await loadFixture(deployZenStakeFixture)

      // 调用合约初始化函数
      await zenStake.initialize(mockToken.target, startBlock, endBlock, zenPerBlock)

      expect(await zenStake.Zen()).to.be.equal(mockToken.target)
      expect(await zenStake.startBlock()).to.be.equal(startBlock)
      expect(await zenStake.endBlock()).to.be.equal(endBlock)
      expect(await zenStake.ZenPerBlock()).to.be.equal(zenPerBlock)
    })

    it('role should be set correctly', async () => {
      const { zenStake, mockToken, owner, startBlock, endBlock, zenPerBlock } =
        await loadFixture(deployZenStakeFixture)

      await zenStake.initialize(mockToken.target, startBlock, endBlock, zenPerBlock)

      expect(await zenStake.hasRole(await zenStake.DEFAULT_ADMIN_ROLE(), owner.address)).to.be.true;
      expect(await zenStake.hasRole(await zenStake.ADMIN_ROLE(), owner.address)).to.be.true;
      expect(await zenStake.hasRole(await zenStake.UPGRADE_ROLE(), owner.address)).to.be.true;
    })

    it('constant should be set correctly', async () => {
      const { zenStake } = await loadFixture(deployZenStakeFixture)

      expect(await zenStake.ETH_PID()).to.equal(0);
      expect(await zenStake.ADMIN_ROLE()).to.equal(ethers.keccak256(ethers.toUtf8Bytes('admin_role')));
      expect(await zenStake.UPGRADE_ROLE()).to.equal(ethers.keccak256(ethers.toUtf8Bytes('upgrade_role')));
    });

    it('variables should be set correctly', async () => {
      const { zenStake, mockToken, startBlock, endBlock, zenPerBlock } =
        await loadFixture(deployZenStakeFixture)

      await zenStake.initialize(mockToken.target, startBlock, endBlock, zenPerBlock)

      expect(await zenStake.withdrawPaused()).to.be.false;
      expect(await zenStake.claimPaused()).to.be.false;
      expect(await zenStake.totalPoolWeight()).to.be.equal(0);
    });

    it('invalid start and end blocks should be rejected', async () => {
      const { zenStake, mockToken, startBlock, zenPerBlock } =
        await loadFixture(deployZenStakeFixture)

      const invalidEndBlock = startBlock - 1;

      await expect(
        zenStake.initialize(mockToken.target, startBlock, invalidEndBlock, zenPerBlock)
      ).to.be.revertedWith("invalid parameters")
    });

    it('zero per block rewards should be rejected', async () => {
      const { zenStake, mockToken, startBlock, endBlock } =
        await loadFixture(deployZenStakeFixture)

      const zeroZenPerBlock = 0;

      await expect(
        zenStake.initialize(mockToken.target, startBlock, endBlock, zeroZenPerBlock)
      ).to.be.revertedWith("invalid parameters")
    });

    it("duplicate initialization should be rejected", async function () {
      const { zenStake, mockToken, startBlock, endBlock, zenPerBlock } = 
        await loadFixture(deployZenStakeFixture);

      await zenStake.initialize(mockToken.target, startBlock, endBlock, zenPerBlock);

      // 尝试重复初始化
      await expect(
        zenStake.initialize(mockToken.target, startBlock, endBlock, zenPerBlock)
      ).to.be.revertedWithCustomError(zenStake, "InvalidInitialization");
    });
  })

  describe('setZen', () => {
    it('admin should be allowed to set Zen tokens', async () => {
      const { zenStake, mockToken, startBlock, endBlock, zenPerBlock } = 
        await loadFixture(deployZenStakeFixture);

      await zenStake.initialize(mockToken.target, startBlock, endBlock, zenPerBlock);

      const { mockToken: newMockToken } = await deployERC20()

      await expect(zenStake.setZen(newMockToken.target)).to.emit(zenStake, 'SetZen').withArgs(newMockToken.target)
      expect(await zenStake.Zen()).to.be.equal(newMockToken.target)
    });

    it('Zen tokens should be rejected for non-admin settings', async () => {
      const { zenStake, mockToken, user1, startBlock, endBlock, zenPerBlock } = 
        await loadFixture(deployZenStakeFixture);

      await zenStake.initialize(mockToken.target, startBlock, endBlock, zenPerBlock);

      const { mockToken: newMockToken } = await deployERC20()

      await expect(
        zenStake.connect(user1).setZen(newMockToken.target)
      ).to.be.revertedWithCustomError(zenStake, 'AccessControlUnauthorizedAccount')
    });
  })

  describe('set withdraw status', () => {
    it('admin should be allowed to pause', async () => {
      const { zenStake, mockToken, startBlock, endBlock, zenPerBlock } = 
        await loadFixture(deployZenStakeFixture);

      await zenStake.initialize(mockToken.target, startBlock, endBlock, zenPerBlock);

      expect(await zenStake.withdrawPaused()).to.be.false;
      await expect(zenStake.pauseWithdraw()).to.emit(zenStake, "PauseWithdraw")
      expect(await zenStake.withdrawPaused()).to.be.true;
    });

    it('admin should be allowed to unpause', async () => {
      const { zenStake, mockToken, startBlock, endBlock, zenPerBlock } = 
        await loadFixture(deployZenStakeFixture);

      await zenStake.initialize(mockToken.target, startBlock, endBlock, zenPerBlock);

      await zenStake.pauseWithdraw()
      expect(await zenStake.withdrawPaused()).to.be.true;

      await expect(zenStake.unpauseWithdraw()).to.emit(zenStake, "UnpauseWithdraw")
      expect(await zenStake.withdrawPaused()).to.be.false;
    });

    it('not admin operations should be refused', async () => {
      const { zenStake, mockToken, user1, startBlock, endBlock, zenPerBlock } = 
        await loadFixture(deployZenStakeFixture);

      await zenStake.initialize(mockToken.target, startBlock, endBlock, zenPerBlock);

      await expect(zenStake.connect(user1).pauseWithdraw()).to.be.revertedWithCustomError(zenStake, 'AccessControlUnauthorizedAccount')
      
      await expect(zenStake.connect(user1).unpauseWithdraw()).to.be.revertedWithCustomError(zenStake, 'AccessControlUnauthorizedAccount')
    });

    it('repeated pauses should be refused', async () => {
      const { zenStake, mockToken, startBlock, endBlock, zenPerBlock } = 
        await loadFixture(deployZenStakeFixture);

      await zenStake.initialize(mockToken.target, startBlock, endBlock, zenPerBlock);
      await zenStake.pauseWithdraw()

      await expect(zenStake.pauseWithdraw()).to.be.revertedWith('withdraw has been already paused')
    });

    it('repeated unpause should be refused', async () => {
      const { zenStake, mockToken, startBlock, endBlock, zenPerBlock } = 
        await loadFixture(deployZenStakeFixture);

      await zenStake.initialize(mockToken.target, startBlock, endBlock, zenPerBlock);

      await expect(zenStake.unpauseWithdraw()).to.be.revertedWith('withdraw has been already unpaused')
    });

  })

  describe('set claim status', () => {
    it('admin should be allowed to pause', async () => {
      const { zenStake, mockToken, startBlock, endBlock, zenPerBlock } = 
        await loadFixture(deployZenStakeFixture);

      await zenStake.initialize(mockToken.target, startBlock, endBlock, zenPerBlock);

      expect(await zenStake.claimPaused()).to.be.false;
      await expect(zenStake.pauseClaim()).to.emit(zenStake, "PauseClaim")
      expect(await zenStake.claimPaused()).to.be.true;
    });

    it('admin should be allowed to unpause', async () => {
      const { zenStake, mockToken, startBlock, endBlock, zenPerBlock } = 
        await loadFixture(deployZenStakeFixture);

      await zenStake.initialize(mockToken.target, startBlock, endBlock, zenPerBlock);

      await zenStake.pauseClaim()
      expect(await zenStake.claimPaused()).to.be.true;

      await expect(zenStake.unpauseClaim()).to.emit(zenStake, "UnpauseClaim")
      expect(await zenStake.claimPaused()).to.be.false;
    });

    it('not admin operations should be refused', async () => {
      const { zenStake, mockToken, user1, startBlock, endBlock, zenPerBlock } = 
        await loadFixture(deployZenStakeFixture);

      await zenStake.initialize(mockToken.target, startBlock, endBlock, zenPerBlock);

      await expect(zenStake.connect(user1).pauseClaim()).to.be.revertedWithCustomError(zenStake, 'AccessControlUnauthorizedAccount')
      
      await expect(zenStake.connect(user1).unpauseClaim()).to.be.revertedWithCustomError(zenStake, 'AccessControlUnauthorizedAccount')
    });

    it('repeated pauses should be refused', async () => {
      const { zenStake, mockToken, startBlock, endBlock, zenPerBlock } = 
        await loadFixture(deployZenStakeFixture);

      await zenStake.initialize(mockToken.target, startBlock, endBlock, zenPerBlock);
      await zenStake.pauseClaim()

      await expect(zenStake.pauseClaim()).to.be.revertedWith('claim has been already paused')
    });

    it('repeated unpause should be refused', async () => {
      const { zenStake, mockToken, startBlock, endBlock, zenPerBlock } = 
        await loadFixture(deployZenStakeFixture);

      await zenStake.initialize(mockToken.target, startBlock, endBlock, zenPerBlock);

      await expect(zenStake.unpauseClaim()).to.be.revertedWith('claim has been already unpaused')
    });
  })

  describe('setZenPerBlock', () => {
    it('admin should be allowed to set zen per block', async () => {
      const { zenStake, mockToken, startBlock, endBlock, zenPerBlock } = 
        await loadFixture(deployZenStakeFixture);

      await zenStake.initialize(mockToken.target, startBlock, endBlock, zenPerBlock);

      const newZenPerBlock = ethers.parseEther('2');

      await expect(zenStake.setZenPerBlock(newZenPerBlock)).to.be.emit(zenStake, 'SetZenPerBlock').withArgs(newZenPerBlock)
      expect(await zenStake.ZenPerBlock()).to.be.equal(newZenPerBlock);
    });

    it('non-admin should not be allowed to set zen per block', async () => {
      const { zenStake, mockToken, user1, startBlock, endBlock, zenPerBlock } = 
        await loadFixture(deployZenStakeFixture);

      await zenStake.initialize(mockToken.target, startBlock, endBlock, zenPerBlock);

      const newZenPerBlock = ethers.parseEther('2');
      await expect(zenStake.connect(user1).setZenPerBlock(newZenPerBlock)).to.be.revertedWithCustomError(zenStake, 'AccessControlUnauthorizedAccount')
    });

    it('zen per block should not be zero', async () => {
      const { zenStake, mockToken, user1, startBlock, endBlock, zenPerBlock } = 
        await loadFixture(deployZenStakeFixture);

      await zenStake.initialize(mockToken.target, startBlock, endBlock, zenPerBlock);

      const newZenPerBlock = ethers.parseEther('0');
      await expect(zenStake.setZenPerBlock(newZenPerBlock)).to.be.revertedWith('invalid parameter')
    });
  })

  describe('addPool', () => {
    it('admin should be allowed to add ETH pool', async () => {
      const { zenStake, mockToken, startBlock, endBlock, zenPerBlock } = 
        await loadFixture(deployZenStakeFixture);

      await zenStake.initialize(mockToken.target, startBlock, endBlock, zenPerBlock);

      const poolWeight = 100;
      const minDepositAmount = ethers.parseEther('0.1');
      const unstakeLockedBlocks = 100;

      await expect(zenStake.addPool(ethers.ZeroAddress, poolWeight, minDepositAmount, unstakeLockedBlocks, false))
        .to.emit(zenStake, 'AddPool')
      
      expect(await zenStake.poolLength()).to.equal(1)
      expect(await zenStake.totalPoolWeight()).to.equal(poolWeight)

      const pool = await zenStake.pool(0)
      expect(pool.stTokenAddress).to.equal(ethers.ZeroAddress)
      expect(pool.poolWeight).to.equal(poolWeight)
      expect(pool.minDepositAmount).to.equal(minDepositAmount)
      expect(pool.unstakeLockedBlocks).to.equal(unstakeLockedBlocks)
    })

    it('admin should be allowed to add ERC20 pool after ETH pool', async () => {
      const { zenStake, mockToken, startBlock, endBlock, zenPerBlock } = 
        await loadFixture(deployZenStakeFixture);

      await zenStake.initialize(mockToken.target, startBlock, endBlock, zenPerBlock);

      const poolWeight = 100;
      const minDepositAmount = ethers.parseEther('0.1');
      const unstakeLockedBlocks = 100;

      await zenStake.addPool(ethers.ZeroAddress, poolWeight, minDepositAmount, unstakeLockedBlocks, false)

      const { mockToken: newMockToken } = await deployERC20()

      await expect(zenStake.addPool(newMockToken.target, poolWeight, minDepositAmount, unstakeLockedBlocks, false))
        .to.emit(zenStake, 'AddPool')
      expect(await zenStake.poolLength()).to.equal(2)
      expect(await zenStake.totalPoolWeight()).to.equal(poolWeight + poolWeight)

      const pool = await zenStake.pool(1)
      expect(pool.stTokenAddress).to.equal(newMockToken.target)
      expect(pool.poolWeight).to.equal(poolWeight)
      expect(pool.minDepositAmount).to.equal(minDepositAmount)
      expect(pool.unstakeLockedBlocks).to.equal(unstakeLockedBlocks)
    })

    it('non-admin should not be allowed to add pool', async () => {
      const { zenStake, mockToken, user1, startBlock, endBlock, zenPerBlock } = 
        await loadFixture(deployZenStakeFixture);

      await zenStake.initialize(mockToken.target, startBlock, endBlock, zenPerBlock);

      await expect(
        zenStake.connect(user1).addPool(ethers.ZeroAddress, 100, ethers.parseEther('0.1'), 100, false)
        ).to.revertedWithCustomError(zenStake, 'AccessControlUnauthorizedAccount')
    })

    it('first pool must be ETH pool', async () => {
      const { zenStake, mockToken, user1, startBlock, endBlock, zenPerBlock } = 
        await loadFixture(deployZenStakeFixture);

      await zenStake.initialize(mockToken.target, startBlock, endBlock, zenPerBlock);

      const { mockToken: newMockToken } = await deployERC20()

      await expect(
        zenStake.addPool(newMockToken.target, 100, ethers.parseEther('0.1'), 100, false)
        ).to.rejectedWith('invalid stTokenAddress')
    })

    it('non-first pool cannot be ETH pool', async () => {
      const { zenStake, mockToken, startBlock, endBlock, zenPerBlock } = 
        await loadFixture(deployZenStakeFixture);

      await zenStake.initialize(mockToken.target, startBlock, endBlock, zenPerBlock);

      await zenStake.addPool(ethers.ZeroAddress, 100, ethers.parseEther('0.1'), 100, false)

      await expect(
        zenStake.addPool(ethers.ZeroAddress, 100, ethers.parseEther('0.1'), 100, false)
        ).to.rejectedWith('invalid stTokenAddress')
    })

    it('unstake locked blocks must be greater than 0', async () => {
      const { zenStake, mockToken, user1, startBlock, endBlock, zenPerBlock } = 
        await loadFixture(deployZenStakeFixture);

      await zenStake.initialize(mockToken.target, startBlock, endBlock, zenPerBlock);

      const unstakeLockedBlocks = 0;

      await expect(
        zenStake.addPool(ethers.ZeroAddress, 100, ethers.parseEther('0.1'), unstakeLockedBlocks, false)
        ).to.rejectedWith('invalid withdraw locked blocks')
    })

    it('should handle mining period end correctly', async () => {
      const { zenStake, mockToken, startBlock, endBlock, zenPerBlock } =
        await loadFixture(deployZenStakeFixture);

      await zenStake.initialize(mockToken.target, startBlock, endBlock, zenPerBlock);

      // 等到挖矿结束后尝试添加池
      await ethers.provider.send("hardhat_mine", [ethers.toQuantity(endBlock - await ethers.provider.getBlockNumber() + 5)]);

      await expect(
        zenStake.addPool(ethers.ZeroAddress, 100, ethers.parseEther('0.1'), 100, false)
      ).to.be.revertedWith('Already ended');
    });
  })

  describe('setPoolWeight', () => {
    it('admin should be allowed to set pool weight', async () => {
      const { zenStake, mockToken, startBlock, endBlock, zenPerBlock } = 
        await loadFixture(deployZenStakeFixture);

      await zenStake.initialize(mockToken.target, startBlock, endBlock, zenPerBlock);
      await zenStake.addPool(ethers.ZeroAddress, 100, ethers.parseEther('0.1'), 100, false)
      
      const newPoolWeight = 200;
      await expect(
        zenStake.setPoolWeight(0, newPoolWeight, false)
        ).to.emit(zenStake, 'SetPoolWeight').withArgs(0, newPoolWeight, newPoolWeight);

      const pool = await zenStake.pool(0)
      expect(pool.poolWeight).to.equal(newPoolWeight);
      expect(await zenStake.totalPoolWeight()).to.equal(newPoolWeight)
    })

    it('non-admin should not be allowed to set pool weight', async () => {
      const { zenStake, mockToken, user1, startBlock, endBlock, zenPerBlock } = 
        await loadFixture(deployZenStakeFixture);

      await zenStake.initialize(mockToken.target, startBlock, endBlock, zenPerBlock);
      await zenStake.addPool(ethers.ZeroAddress, 100, ethers.parseEther('0.1'), 100, false)

      await expect(
        zenStake.connect(user1).setPoolWeight(0, 200, false)
        ).to.revertedWithCustomError(zenStake, 'AccessControlUnauthorizedAccount')
    })

    it('non-admin should not be allowed to set pool weight', async () => {
      const { zenStake, mockToken, user1, startBlock, endBlock, zenPerBlock } = 
        await loadFixture(deployZenStakeFixture);

      await zenStake.initialize(mockToken.target, startBlock, endBlock, zenPerBlock);
      await zenStake.addPool(ethers.ZeroAddress, 100, ethers.parseEther('0.1'), 100, false)

      await expect(
        zenStake.setPoolWeight(0, 0, false)
        ).to.revertedWith('invalid pool weight')
    })

    it('should reject invalid pool id', async () => {
      const { zenStake, mockToken, startBlock, endBlock, zenPerBlock } =
        await loadFixture(deployZenStakeFixture);

      await zenStake.initialize(mockToken.target, startBlock, endBlock, zenPerBlock);

      await expect(
        zenStake.setPoolWeight(0, 200, false)
      ).to.be.revertedWith('invalid pid');
    });

    it('should update total pool weight correctly with multiple pools', async () => {
      const { zenStake, mockToken, startBlock, endBlock, zenPerBlock } = 
        await loadFixture(deployZenStakeFixture);

      const firstPoolWeight = 100;
      const secondPoolWeight = 200;
      const modifyFirstPoolWeight = 150;

      await zenStake.initialize(mockToken.target, startBlock, endBlock, zenPerBlock);
      await zenStake.addPool(ethers.ZeroAddress, firstPoolWeight, ethers.parseEther('0.1'), 100, false)

      const { mockToken: newMockToken } = await deployERC20()
      await zenStake.addPool(newMockToken.target, secondPoolWeight, ethers.parseEther('0.1'), 100, false)

      expect(
        await zenStake.totalPoolWeight()
        ).to.equal(firstPoolWeight + secondPoolWeight)

      await zenStake.setPoolWeight(0, modifyFirstPoolWeight, false);
      expect(
        await zenStake.totalPoolWeight()
        ).to.equal(secondPoolWeight + modifyFirstPoolWeight)
    })
  })

  describe('massUpdatePools', () => {
    it('should update pool rewards correctly when mining has started', async () => {
      const { zenStake, mockToken, startBlock, endBlock, zenPerBlock } =
        await loadFixture(deployZenStakeFixture);

      await zenStake.initialize(mockToken.target, startBlock, endBlock, zenPerBlock);
      await zenStake.addPool(ethers.ZeroAddress, 100, ethers.parseEther('0.1'), 100, false);

      // 等到挖矿开始后
      await ethers.provider.send("hardhat_mine", [ethers.toQuantity(startBlock - await ethers.provider.getBlockNumber() + 5)]);

      const poolBefore = await zenStake.pool(0);
      const lastRewardBlockBefore = poolBefore.lastRewardBlock;

      await expect(zenStake['updatePool(uint256)'](0))
        .to.emit(zenStake, "UpdatePool");

      const poolAfter = await zenStake.pool(0);
      expect(poolAfter.lastRewardBlock).to.be.greaterThan(lastRewardBlockBefore);
    });

    it('should not emit event when no time has passed', async () => {
      const { zenStake, mockToken, startBlock, endBlock, zenPerBlock } =
        await loadFixture(deployZenStakeFixture);

      await zenStake.initialize(mockToken.target, startBlock, endBlock, zenPerBlock);
      await zenStake.addPool(ethers.ZeroAddress, 100, ethers.parseEther('0.1'), 100, false);

      // 立即调用updatePool
      await expect(zenStake['updatePool(uint256)'](0))
        .to.not.emit(zenStake, "UpdatePool");
    });

    it('should reject invalid pool id', async () => {
      const { zenStake, mockToken, startBlock, endBlock, zenPerBlock } =
        await loadFixture(deployZenStakeFixture);

      await zenStake.initialize(mockToken.target, startBlock, endBlock, zenPerBlock);

      await expect(
        zenStake['updatePool(uint256)'](0)
      ).to.be.revertedWith('invalid pid');
    });
  })

  describe('deposit', () => {
    it('should allow ETH deposit to ETH pool', async () => {
      const { zenStake, mockToken, user1, startBlock, endBlock, zenPerBlock } =
        await loadFixture(deployZenStakeFixture);

      await zenStake.initialize(mockToken.target, startBlock, endBlock, zenPerBlock);
      await zenStake.addPool(ethers.ZeroAddress, 100, ethers.parseEther('0.1'), 100, false);

      const depositAmount = ethers.parseEther('1');

      await expect(
        zenStake.connect(user1).depositETH({value: depositAmount})
      ).to.emit(zenStake, 'Deposit').withArgs(user1.address, 0, depositAmount)

      expect(await zenStake.stakingBalance(0, user1.address)).to.equal(depositAmount)
      expect((await zenStake.pool(0)).stTokenAmount).to.equal(depositAmount)
    })

    it('should reject deposit when no ETH pool exists', async () => {
      const { zenStake, mockToken, startBlock, endBlock, zenPerBlock, user1 } =
        await loadFixture(deployZenStakeFixture);

      await zenStake.initialize(mockToken.target, startBlock, endBlock, zenPerBlock);

      const depositAmount = ethers.parseEther('1');

      await expect(
        zenStake.connect(user1).depositETH({ value: depositAmount })
      ).to.be.revertedWithPanic(0x32);
    });

    it('should reject deposit below minimum amount', async () => {
      const { zenStake, mockToken, user1, startBlock, endBlock, zenPerBlock } =
        await loadFixture(deployZenStakeFixture);

      await zenStake.initialize(mockToken.target, startBlock, endBlock, zenPerBlock);
      await zenStake.addPool(ethers.ZeroAddress, 100, ethers.parseEther('1'), 100, false);

      const depositAmount = ethers.parseEther('0.1');

      await expect(
        zenStake.connect(user1).depositETH({value: depositAmount})
      ).to.revertedWith('deposit amount is too small')
    })

    it('should accumulate multiple deposits', async () => {
      const { zenStake, mockToken, startBlock, endBlock, zenPerBlock, user1 } =
        await loadFixture(deployZenStakeFixture);

      await zenStake.initialize(mockToken.target, startBlock, endBlock, zenPerBlock);
      await zenStake.addPool(ethers.ZeroAddress, 100, ethers.parseEther('0.1'), 100, false);

      const depositAmount1 = ethers.parseEther('1');
      const depositAmount2 = ethers.parseEther('0.5');

      await zenStake.connect(user1).depositETH({ value: depositAmount1 });
      await zenStake.connect(user1).depositETH({ value: depositAmount2 });

      expect(await zenStake.stakingBalance(0, user1.address)).to.equal(depositAmount1 + depositAmount2);
      expect((await zenStake.pool(0)).stTokenAmount).to.equal(depositAmount1 + depositAmount2);
    });

    it('should handle multiple users correctly', async () => {
      const { zenStake, mockToken, startBlock, endBlock, zenPerBlock, user1, user2 } =
        await loadFixture(deployZenStakeFixture);

      await zenStake.initialize(mockToken.target, startBlock, endBlock, zenPerBlock);
      await zenStake.addPool(ethers.ZeroAddress, 100, ethers.parseEther('0.1'), 100, false);

      // 多个用户同时质押
      await zenStake.connect(user1).depositETH({ value: ethers.parseEther('1') });
      await zenStake.connect(user2).depositETH({ value: ethers.parseEther('2') });

      expect(await zenStake.stakingBalance(0, user1.address)).to.equal(ethers.parseEther('1'));
      expect(await zenStake.stakingBalance(0, user2.address)).to.equal(ethers.parseEther('2'));

      const pool = await zenStake.pool(0);
      expect(pool.stTokenAmount).to.equal(ethers.parseEther('3'));
    });
  })

  describe('unstake', () => {
    it('should allow unstaking', async () => {
      const { zenStake, mockToken, user1, startBlock, endBlock, zenPerBlock } =
        await loadFixture(deployZenStakeFixture);

      await zenStake.initialize(mockToken.target, startBlock, endBlock, zenPerBlock);
      await zenStake.addPool(ethers.ZeroAddress, 100, ethers.parseEther('0.1'), 100, false);

      const depositAmount = ethers.parseEther('1');
      const unstakeAmount = ethers.parseEther('0.5');

      await zenStake.connect(user1).depositETH({ value: depositAmount });

      await expect(
        zenStake.connect(user1).unstake(0, unstakeAmount)
      ).to.emit(zenStake, 'RequestUnstake').withArgs(user1.address, 0, unstakeAmount)

      expect(await zenStake.stakingBalance(0, user1.address)).to.equal(depositAmount - unstakeAmount);

      const [requestAmount, pendingWithdrawAmount] = await zenStake.withdrawAmount(0, user1.address)
      expect(requestAmount).to.equal(unstakeAmount);
      expect(pendingWithdrawAmount).to.equal(0);
    })

    it('should reject unstaking more than balance', async () => {
      const { zenStake, mockToken, user1, startBlock, endBlock, zenPerBlock } =
        await loadFixture(deployZenStakeFixture);

      await zenStake.initialize(mockToken.target, startBlock, endBlock, zenPerBlock);
      await zenStake.addPool(ethers.ZeroAddress, 100, ethers.parseEther('0.1'), 100, false);

      const depositAmount = ethers.parseEther('1');
      const unstakeAmount = ethers.parseEther('1.5');

      await zenStake.connect(user1).depositETH({ value: depositAmount });

      await expect(
        zenStake.connect(user1).unstake(0, unstakeAmount)
      ).to.rejectedWith('Not enough staking token balance')
    })

    it('should reject unstaking when withdraw is paused', async () => {
      const { zenStake, mockToken, user1, startBlock, endBlock, zenPerBlock } =
        await loadFixture(deployZenStakeFixture);

      await zenStake.initialize(mockToken.target, startBlock, endBlock, zenPerBlock);
      await zenStake.addPool(ethers.ZeroAddress, 100, ethers.parseEther('0.1'), 100, false);

      const depositAmount = ethers.parseEther('1');
      const unstakeAmount = ethers.parseEther('0.5');

      await zenStake.connect(user1).depositETH({ value: depositAmount });

      await zenStake.pauseWithdraw()

      await expect(
        zenStake.connect(user1).unstake(0, unstakeAmount)
      ).to.rejectedWith('withdraw is paused')
    })

    it('should handle zero unstake correctly', async () => {
      const { zenStake, mockToken, startBlock, endBlock, zenPerBlock, user1 } =
        await loadFixture(deployZenStakeFixture);

      await zenStake.initialize(mockToken.target, startBlock, endBlock, zenPerBlock);
      await zenStake.addPool(ethers.ZeroAddress, 100, ethers.parseEther('0.1'), 100, false);

      await zenStake.connect(user1).depositETH({ value: ethers.parseEther('1') });

      // 零金额解质押应该正常工作
      await expect(
        zenStake.connect(user1).unstake(0, 0)
      ).to.emit(zenStake, "RequestUnstake")
        .withArgs(user1.address, 0, 0);
    });

    it('should handle contract state consistency', async () => {
      const { zenStake, mockToken, startBlock, endBlock, zenPerBlock, user1 } =
        await loadFixture(deployZenStakeFixture);

      await zenStake.initialize(mockToken.target, startBlock, endBlock, zenPerBlock);
      await zenStake.addPool(ethers.ZeroAddress, 100, ethers.parseEther('0.1'), 100, false);

      const depositAmount = ethers.parseEther('1');
      const unstakeAmount = ethers.parseEther('0.3');

      await zenStake.connect(user1).depositETH({ value: depositAmount });
      await zenStake.connect(user1).unstake(0, unstakeAmount);

      // 检查状态一致性
      const userBalance = await zenStake.stakingBalance(0, user1.address);
      const pool = await zenStake.pool(0);
      const [requestAmount] = await zenStake.withdrawAmount(0, user1.address);

      expect(userBalance).to.equal(depositAmount - unstakeAmount);
      expect(pool.stTokenAmount).to.equal(depositAmount - unstakeAmount);
      expect(requestAmount).to.equal(unstakeAmount);
    });
  })

  describe('withdraw', () => {
    it('should allow withdrawal after unlock period', async () => {
      const { zenStake, mockToken, user1, startBlock, endBlock, zenPerBlock } =
        await loadFixture(deployZenStakeFixture);

      await zenStake.initialize(mockToken.target, startBlock, endBlock, zenPerBlock);
      await zenStake.addPool(ethers.ZeroAddress, 100, ethers.parseEther('0.1'), 100, false);

      const depositAmount = ethers.parseEther('1');
      const unstakeAmount = ethers.parseEther('0.5');

      await zenStake.connect(user1).depositETH({ value: depositAmount });
      await zenStake.connect(user1).unstake(0, unstakeAmount);

      await ethers.provider.send("hardhat_mine", [ethers.toQuantity(101)]); // 第101个区块

      await expect(
        zenStake.connect(user1).withdraw(0)
      ).to.emit(zenStake, 'Withdraw')
        .withArgs(user1.address, 0, unstakeAmount, await ethers.provider.getBlockNumber() + 1)
      
      // 检查质押是否被清空
      const [requestAmount, pendingWithdrawAmount] = await zenStake.withdrawAmount(0, user1.address)
      expect(requestAmount).to.equal(0);
      expect(pendingWithdrawAmount).to.equal(0);
    })

    it('should withdraw available amount based on unlock time', async () => {
      const { zenStake, mockToken, startBlock, endBlock, zenPerBlock, user1 } =
        await loadFixture(deployZenStakeFixture);

      await zenStake.initialize(mockToken.target, startBlock, endBlock, zenPerBlock);
      await zenStake.addPool(ethers.ZeroAddress, 100, ethers.parseEther('0.1'), 100, false);

      const depositAmount = ethers.parseEther('1');
      const unstakeAmount = ethers.parseEther('0.5');

      await zenStake.connect(user1).depositETH({ value: depositAmount });
      await zenStake.connect(user1).unstake(0, unstakeAmount);

      // 立即withdraw不会提取任何金额（还没解锁）
      await expect(
        zenStake.connect(user1).withdraw(0)
      ).to.emit(zenStake, "Withdraw")
        .withArgs(user1.address, 0, 0, await ethers.provider.getBlockNumber() + 1); // 实际上不会提取任何金额
    });

    it('should reject withdrawal when withdraw is paused', async () => {
      const { zenStake, mockToken, startBlock, endBlock, zenPerBlock, user1 } =
        await loadFixture(deployZenStakeFixture);

      await zenStake.initialize(mockToken.target, startBlock, endBlock, zenPerBlock);
      await zenStake.addPool(ethers.ZeroAddress, 100, ethers.parseEther('0.1'), 100, false);

      await zenStake.pauseWithdraw()

      await expect(
        zenStake.connect(user1).withdraw(0)
      ).to.revertedWith('withdraw is paused')
    })

    it('should handle multiple unstake requests correctly', async () => {
      const { zenStake, mockToken, startBlock, endBlock, zenPerBlock, user1 } =
        await loadFixture(deployZenStakeFixture);

      await zenStake.initialize(mockToken.target, startBlock, endBlock, zenPerBlock);
      await zenStake.addPool(ethers.ZeroAddress, 100, ethers.parseEther('0.1'), 100, false);

      const depositAmount = ethers.parseEther('2');
      await zenStake.connect(user1).depositETH({ value: depositAmount });

      // 多次解质押
      const firstUnstake = ethers.parseEther('0.5');
      const secondUnstake = ethers.parseEther('0.3');

      await zenStake.connect(user1).unstake(0, firstUnstake);
      await ethers.provider.send("hardhat_mine", [ethers.toQuantity(50)]);  // 第50个区块
      await zenStake.connect(user1).unstake(0, secondUnstake);
      await ethers.provider.send("hardhat_mine", [ethers.toQuantity(51)]);  // 第51个区块

      await expect(
        zenStake.connect(user1).withdraw(0)
      ).to.emit(zenStake, 'Withdraw')
        .withArgs(user1.address, 0, firstUnstake, await ethers.provider.getBlockNumber() + 1)
      
      // 检查质押是否被清空
      const [requestAmount, pendingWithdrawAmount] = await zenStake.withdrawAmount(0, user1.address)
      expect(requestAmount).to.equal(secondUnstake);
      expect(pendingWithdrawAmount).to.equal(0);
    })
  })

  describe('claim', () => {
    it('should allow claiming rewards', async () => {
      const { zenStake, mockToken, startBlock, endBlock, zenPerBlock, user1 } =
        await loadFixture(deployZenStakeFixture);

      await zenStake.initialize(mockToken.target, startBlock, endBlock, zenPerBlock);
      await zenStake.addPool(ethers.ZeroAddress, 100, ethers.parseEther('0.1'), 100, false);

      // 转入奖励代币
      mockToken.transfer(zenStake.target, ethers.parseEther('1000'))
      // 质押代币
      const depositAmount = ethers.parseEther('1');
      await zenStake.connect(user1).depositETH({value: depositAmount})

      // 挖出区块
      await ethers.provider.send("hardhat_mine", [ethers.toQuantity(startBlock - await ethers.provider.getBlockNumber() + 10)]);

      // 查看池中的奖励代币
      const pendingBefore = await zenStake.pendingZen(0, user1.address);
      expect(pendingBefore).to.be.greaterThan(0) // 是否大于0

      await expect(
        zenStake.connect(user1).claim(0)
      ).to.emit(zenStake, "Claim");

      // 检查用户获得了奖励代币（由于claim时会再挖一个区块，所以奖励会增加，是一个不确定的值）
      const userBalance = await mockToken.balanceOf(user1.address);
      expect(userBalance).to.be.greaterThan(pendingBefore);
    })

    it('should reject claim when paused', async () => {
      const { zenStake, mockToken, startBlock, endBlock, zenPerBlock, user1 } =
        await loadFixture(deployZenStakeFixture);

      await zenStake.initialize(mockToken.target, startBlock, endBlock, zenPerBlock);
      await zenStake.addPool(ethers.ZeroAddress, 100, ethers.parseEther('0.1'), 100, false);

      await zenStake.connect(user1).depositETH({ value: ethers.parseEther('1') });
      await zenStake.pauseClaim(); // 暂停领取功能

      await expect(
        zenStake.connect(user1).claim(0)
      ).to.be.revertedWith('claim is paused');
    });

    it('should handle zero rewards correctly', async () => {
      const { zenStake, mockToken, startBlock, endBlock, zenPerBlock, user1 } =
        await loadFixture(deployZenStakeFixture);

      await zenStake.initialize(mockToken.target, startBlock, endBlock, zenPerBlock);
      await zenStake.addPool(ethers.ZeroAddress, 100, ethers.parseEther('0.1'), 100, false);

      await zenStake.connect(user1).depositETH({ value: ethers.parseEther('1') });

      // 立即领取（没有奖励）
      await expect(
        zenStake.connect(user1).claim(0)
      ).to.emit(zenStake, "Claim")
        .withArgs(user1.address, 0, 0);
    });
  })

  describe('pendingZen', () => {
    it('should calculate pending rewards correctly', async () => {
      const { zenStake, mockToken, startBlock, endBlock, zenPerBlock, user1 } =
        await loadFixture(deployZenStakeFixture);

      await zenStake.initialize(mockToken.target, startBlock, endBlock, zenPerBlock);
      await zenStake.addPool(ethers.ZeroAddress, 100, ethers.parseEther('0.1'), 100, false);

      const depositAmount = ethers.parseEther('1');
      await zenStake.connect(user1).depositETH({ value: depositAmount });

      // 开始挖矿
      await ethers.provider.send('hardhat_mine', [50])

      const pendingZen = await zenStake.pendingZen(0, user1.address)
      expect(pendingZen).to.be.greaterThan(0)

      // 继续挖矿
      await ethers.provider.send('hardhat_mine', [55])

      const pendingAfter = await zenStake.pendingZen(0, user1.address)
      expect(pendingAfter).to.be.greaterThan(pendingZen)
    })

    it('should return zero for non-stakers', async () => {
      const { zenStake, mockToken, startBlock, endBlock, zenPerBlock, user1 } =
        await loadFixture(deployZenStakeFixture);

      await zenStake.initialize(mockToken.target, startBlock, endBlock, zenPerBlock);
      await zenStake.addPool(ethers.ZeroAddress, 100, ethers.parseEther('0.1'), 100, false);

      const pending = await zenStake.pendingZen(0, user1.address);
      expect(pending).to.equal(0);
    });

    it('should reject invalid pool id', async () => {
      const { zenStake, mockToken, startBlock, endBlock, zenPerBlock, user1 } =
        await loadFixture(deployZenStakeFixture);

      await zenStake.initialize(mockToken.target, startBlock, endBlock, zenPerBlock);

      await expect(
        zenStake.pendingZen(0, user1.address)
      ).to.be.revertedWith('invalid pid');
    });
  })

  describe('pendingZenByBlockNumber', () => {
    it('should calculate rewards for specific block number', async () => {
      const { zenStake, mockToken, startBlock, endBlock, zenPerBlock, user1 } =
        await loadFixture(deployZenStakeFixture);

      await zenStake.initialize(mockToken.target, startBlock, endBlock, zenPerBlock);
      await zenStake.addPool(ethers.ZeroAddress, 100, ethers.parseEther('0.1'), 100, false);

      await zenStake.connect(user1).depositETH({ value: ethers.parseEther('1') });

      const currentBlock = await ethers.provider.getBlockNumber();
      const futureBlock = currentBlock + 100;
      const pendingFuture = await zenStake.pendingZenByBlockNumber(0, user1.address, futureBlock)
      expect(pendingFuture).to.be.greaterThan(0)

      const pendingFuture2 = await zenStake.pendingZenByBlockNumber(0, user1.address, futureBlock + 50);
      expect(pendingFuture2).to.be.greaterThan(pendingFuture);
    })

    it('should calculate rewards for specific block number', async () => {
      const { zenStake, mockToken, startBlock, endBlock, zenPerBlock, user1 } =
        await loadFixture(deployZenStakeFixture);

      await zenStake.initialize(mockToken.target, startBlock, endBlock, zenPerBlock);
      await zenStake.addPool(ethers.ZeroAddress, 100, ethers.parseEther('0.1'), 100, false);

      await zenStake.connect(user1).depositETH({ value: ethers.parseEther('1') });

      const pastBlock = Math.max(1, startBlock - 5);
      const pendingFuture = await zenStake.pendingZenByBlockNumber(0, user1.address, pastBlock)
      expect(pendingFuture).to.equal(0)
    })
  })

  describe('withdrawAmount', () => {
    it('should return correct withdraw amounts', async () => {
      const { zenStake, mockToken, startBlock, endBlock, zenPerBlock, user1 } =
        await loadFixture(deployZenStakeFixture);

      await zenStake.initialize(mockToken.target, startBlock, endBlock, zenPerBlock);
      await zenStake.addPool(ethers.ZeroAddress, 100, ethers.parseEther('0.1'), 100, false);

      const depositAmount = ethers.parseEther('1')
      const firstUnstakeAmount = ethers.parseEther('0.3')
      const seconedUnstakeAmount = ethers.parseEther('0.2')

      await zenStake.connect(user1).depositETH({ value: depositAmount });

      // 检查初始化奖励
      let [requestAmount, pendingWithdrawAmount] = await zenStake.withdrawAmount(0, user1.address)
      expect(requestAmount).to.equal(0);
      expect(pendingWithdrawAmount).to.equal(0)

      // 第一次解押
      await zenStake.connect(user1).unstake(0, firstUnstakeAmount);
      // 时间未到
      [requestAmount, pendingWithdrawAmount] = await zenStake.withdrawAmount(0, user1.address)
      expect(requestAmount).to.equal(firstUnstakeAmount);
      expect(pendingWithdrawAmount).to.equal(0)

      await ethers.provider.send('hardhat_mine', [ethers.toQuantity(101)]);
      [requestAmount, pendingWithdrawAmount] = await zenStake.withdrawAmount(0, user1.address)
      expect(requestAmount).to.equal(firstUnstakeAmount);
      expect(pendingWithdrawAmount).to.equal(firstUnstakeAmount)
          
      // 第二次解押
      await zenStake.connect(user1).unstake(0, seconedUnstakeAmount);
      [requestAmount, pendingWithdrawAmount] = await zenStake.withdrawAmount(0, user1.address)
      expect(requestAmount).to.equal(firstUnstakeAmount + seconedUnstakeAmount);
      expect(pendingWithdrawAmount).to.equal(firstUnstakeAmount)
    })

    it('should reject invalid pool id', async () => {
      const { zenStake, mockToken, startBlock, endBlock, zenPerBlock, user1 } =
        await loadFixture(deployZenStakeFixture);

      await zenStake.initialize(mockToken.target, startBlock, endBlock, zenPerBlock);

      await expect(
        zenStake.withdrawAmount(0, user1.address)
      ).to.be.revertedWith('invalid pid');
    });
  })

  describe('getMultiplier', () => {
    it('should calculate multiplier correctly', async () => {
      const { zenStake, mockToken, startBlock, endBlock, zenPerBlock } =
        await loadFixture(deployZenStakeFixture);

      await zenStake.initialize(mockToken.target, startBlock, endBlock, zenPerBlock);

      const fromBlock = startBlock;
      const toBlock = startBlock + 10;
      const expectedMultiplier = BigInt(toBlock - fromBlock) * zenPerBlock;

      const multiplier = await zenStake.getMultiplier(fromBlock, toBlock);
      expect(multiplier).to.equal(expectedMultiplier);
    });

    it('should handle blocks before start block', async () => {
      const { zenStake, mockToken, startBlock, endBlock, zenPerBlock } =
        await loadFixture(deployZenStakeFixture);

      await zenStake.initialize(mockToken.target, startBlock, endBlock, zenPerBlock);

      const fromBlock = startBlock - 10;
      const toBlock = startBlock + 10;
      const expectedMultiplier = BigInt(toBlock - startBlock) * zenPerBlock; // 从startBlock开始计算

      const multiplier = await zenStake.getMultiplier(fromBlock, toBlock);
      expect(multiplier).to.equal(expectedMultiplier);
    });

    it('should handle blocks after end block', async () => {
      const { zenStake, mockToken, startBlock, endBlock, zenPerBlock } =
        await loadFixture(deployZenStakeFixture);

      await zenStake.initialize(mockToken.target, startBlock, endBlock, zenPerBlock);

      const fromBlock = startBlock;
      const toBlock = endBlock + 10;
      const expectedMultiplier = BigInt(endBlock - fromBlock) * zenPerBlock; // 到endBlock结束

      const multiplier = await zenStake.getMultiplier(fromBlock, toBlock);
      expect(multiplier).to.equal(expectedMultiplier);
    });

    it('should reject invalid block range', async () => {
      const { zenStake, mockToken, startBlock, endBlock, zenPerBlock } =
        await loadFixture(deployZenStakeFixture);

      await zenStake.initialize(mockToken.target, startBlock, endBlock, zenPerBlock);

      await expect(
        zenStake.getMultiplier(startBlock + 10, startBlock)
      ).to.be.revertedWith('invalid block');
    });

    it('should reject when from >= to after adjustment', async () => {
      const { zenStake, mockToken, startBlock, endBlock, zenPerBlock } =
        await loadFixture(deployZenStakeFixture);

      await zenStake.initialize(mockToken.target, startBlock, endBlock, zenPerBlock);

      await expect(
        zenStake.getMultiplier(endBlock + 10, endBlock + 20)
      ).to.be.revertedWith('end block must be greater than start block');
    });
  })

  describe('poolLength', () => {
    it('should return correct pool length', async () => {
      const { zenStake, mockToken, startBlock, endBlock, zenPerBlock } =
        await loadFixture(deployZenStakeFixture);

      await zenStake.initialize(mockToken.target, startBlock, endBlock, zenPerBlock);

      expect(await zenStake.poolLength()).to.equal(0);

      await zenStake.addPool(ethers.ZeroAddress, 100, ethers.parseEther('0.1'), 100, false);
      expect(await zenStake.poolLength()).to.equal(1);

      const { mockToken: stakingToken } = await deployERC20();
      await zenStake.addPool(stakingToken.target, 200, ethers.parseEther('10'), 100, false);
      expect(await zenStake.poolLength()).to.equal(2);
    });
  })

  describe('stakingBalance', () => {
    it('should return correct staking balance', async () => {
      const { zenStake, mockToken, startBlock, endBlock, zenPerBlock, user1 } =
        await loadFixture(deployZenStakeFixture);

      await zenStake.initialize(mockToken.target, startBlock, endBlock, zenPerBlock);
      await zenStake.addPool(ethers.ZeroAddress, 100, ethers.parseEther('0.1'), 100, false);

      expect(await zenStake.stakingBalance(0, user1.address)).to.equal(0);

      const depositAmount = ethers.parseEther('1');
      await zenStake.connect(user1).depositETH({ value: depositAmount });
      expect(await zenStake.stakingBalance(0, user1.address)).to.equal(depositAmount);

      const unstakeAmount = ethers.parseEther('0.3');
      await zenStake.connect(user1).unstake(0, unstakeAmount);
      expect(await zenStake.stakingBalance(0, user1.address)).to.equal(depositAmount - unstakeAmount);
    });

    it('should reject invalid pool id', async () => {
      const { zenStake, mockToken, startBlock, endBlock, zenPerBlock, user1 } =
        await loadFixture(deployZenStakeFixture);

      await zenStake.initialize(mockToken.target, startBlock, endBlock, zenPerBlock);

      await expect(
        zenStake.stakingBalance(0, user1.address)
      ).to.be.revertedWith('invalid pid');
    });
  })

})
