import { ethers, upgrades } from "hardhat";

async function main() {
  console.log("开始部署 ZenStake 合约到 Sepolia 测试网...");

  // 获取部署账户
  const [deployer] = await ethers.getSigners();
  console.log("部署账户:", deployer.address);
  console.log("账户余额:", ethers.formatEther(await deployer.provider.getBalance(deployer.address)), "ETH");

  // 获取当前区块号
  const currentBlock = await ethers.provider.getBlockNumber();
  console.log("当前区块号:", currentBlock);

  // 1. 部署 ZenToken 作为奖励代币 (ZEN)
  console.log("\n1. 部署 ZEN 奖励代币...");
  const ZenToken = await ethers.getContractFactory("ZenToken");
  const zenToken = await ZenToken.deploy(deployer.address);
  await zenToken.waitForDeployment();
  const zenTokenAddress = await zenToken.getAddress();
  console.log("ZEN Token 部署地址:", zenTokenAddress);

  // 获取代币基本信息
  const tokenName = await (zenToken as any).name();
  const tokenSymbol = await (zenToken as any).symbol();
  const tokenDecimals = await (zenToken as any).decimals();
  const totalSupply = await (zenToken as any).totalSupply();
  console.log("代币信息:");
  console.log("- 名称:", tokenName);
  console.log("- 符号:", tokenSymbol);
  console.log("- 小数位:", tokenDecimals);
  console.log("- 总供应量:", ethers.formatEther(totalSupply), "ZEN");

  // 2. 部署可升级的 ZenStake 合约
  console.log("\n2. 部署可升级的 ZenStake 合约...");
  const ZenStakeImpl = await ethers.getContractFactory("ZenStakeImpl");
  
  // 设置初始化参数
  const startBlock = currentBlock + 100; // 100 个区块后开始
  const endBlock = startBlock + 100000;  // 持续 100000 个区块
  const zenPerBlock = ethers.parseEther("1"); // 每区块 1 ZEN

  console.log("初始化参数:");
  console.log("- 开始区块:", startBlock);
  console.log("- 结束区块:", endBlock);
  console.log("- 每区块奖励:", ethers.formatEther(zenPerBlock), "ZEN");

  // 使用 OpenZeppelin 升级插件部署代理合约
  const zenStake = await upgrades.deployProxy(
    ZenStakeImpl,
    [zenTokenAddress, startBlock, endBlock, zenPerBlock],
    { 
      initializer: "initialize",
      kind: "uups" 
    }
  );
  await zenStake.waitForDeployment();
  const zenStakeAddress = await zenStake.getAddress();
  console.log("ZenStake 代理合约地址:", zenStakeAddress);

  // 3. 向 ZenStake 合约转入一些 ZEN 代币作为奖励池
  console.log("\n3. 向质押合约转入奖励代币...");
  const rewardAmount = ethers.parseEther("100000"); // 100,000 ZEN
  await (zenToken as any).transfer(zenStakeAddress, rewardAmount);
  console.log("已向质押合约转入", ethers.formatEther(rewardAmount), "ZEN 作为奖励池");

  // 4. 添加 ETH 质押池 (池 ID 0)
  console.log("\n4. 添加 ETH 质押池...");
  const ethPoolWeight = 100;
  const minDepositAmount = ethers.parseEther("0.01"); // 最小质押 0.01 ETH
  const unstakeLockedBlocks = 1000; // 解锁需要 1000 个区块

  await zenStake.addPool(
    "******************************************", // ETH 地址为 0x0
    ethPoolWeight,
    minDepositAmount,
    unstakeLockedBlocks,
    false
  );
  console.log("ETH 质押池已添加 (池 ID: 0)");
  console.log("- 池权重:", ethPoolWeight);
  console.log("- 最小质押量:", ethers.formatEther(minDepositAmount), "ETH");
  console.log("- 解锁区块数:", unstakeLockedBlocks);

  // 5. 部署总结
  console.log("\n=== 部署完成 ===");
  console.log("ZEN Token 地址:", zenTokenAddress);
  console.log("ZenStake 代理合约地址:", zenStakeAddress);
  console.log("部署账户:", deployer.address);
  console.log("网络:", (await ethers.provider.getNetwork()).name);

  // 6. 保存部署信息到文件
  const deploymentInfo = {
    network: (await ethers.provider.getNetwork()).name,
    chainId: (await ethers.provider.getNetwork()).chainId,
    deployer: deployer.address,
    zenToken: zenTokenAddress,
    zenStake: zenStakeAddress,
    startBlock,
    endBlock,
    zenPerBlock: zenPerBlock.toString(),
    deployedAt: new Date().toISOString(),
    blockNumber: await ethers.provider.getBlockNumber()
  };

  console.log("\n部署信息已保存，请手动创建 deployments.json 文件保存以下信息:");
  console.log(JSON.stringify(deploymentInfo, null, 2));
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error("部署失败:", error);
    process.exit(1);
  });
