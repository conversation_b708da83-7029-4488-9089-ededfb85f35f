import { ethers } from "hardhat";

async function main() {
  console.log("开始部署 ZEN Token 到 Sepolia 测试网...");

  // 获取部署账户
  const [deployer] = await ethers.getSigners();
  console.log("部署账户:", deployer.address);
  console.log("账户余额:", ethers.formatEther(await deployer.provider.getBalance(deployer.address)), "ETH");

  // 部署 ZenToken
  console.log("\n部署 ZEN Token...");
  const ZenToken = await ethers.getContractFactory("ZenToken");
  
  console.log("正在部署合约...");
  const zenToken = await ZenToken.deploy(deployer.address);
  
  console.log("等待部署确认...");
  await zenToken.waitForDeployment();
  
  const zenTokenAddress = await zenToken.getAddress();
  console.log("✅ ZEN Token 部署成功!");
  console.log("合约地址:", zenTokenAddress);

  // 获取代币基本信息
  console.log("\n📊 代币信息:");
  try {
    const tokenName = await (zenToken as any).name();
    const tokenSymbol = await (zenToken as any).symbol();
    const tokenDecimals = await (zenToken as any).decimals();
    const totalSupply = await (zenToken as any).totalSupply();
    const deployerBalance = await (zenToken as any).balanceOf(deployer.address);
    
    console.log("- 名称:", tokenName);
    console.log("- 符号:", tokenSymbol);
    console.log("- 小数位:", tokenDecimals.toString());
    console.log("- 总供应量:", ethers.formatEther(totalSupply), "ZEN");
    console.log("- 部署者余额:", ethers.formatEther(deployerBalance), "ZEN");
  } catch (error) {
    console.log("获取代币信息时出错:", error);
  }

  // 获取网络信息
  const network = await ethers.provider.getNetwork();
  const currentBlock = await ethers.provider.getBlockNumber();
  
  console.log("\n🌐 网络信息:");
  console.log("- 网络名称:", network.name);
  console.log("- Chain ID:", network.chainId.toString());
  console.log("- 当前区块:", currentBlock);

  // 保存部署信息
  const deploymentInfo = {
    network: network.name,
    chainId: network.chainId.toString(),
    deployer: deployer.address,
    zenToken: zenTokenAddress,
    deployedAt: new Date().toISOString(),
    blockNumber: currentBlock,
    contractInfo: {
      name: "ZEN Token",
      symbol: "ZEN",
      decimals: 18,
      totalSupply: "1000000000000000000000000000" // 1 billion tokens
    }
  };

  console.log("\n📄 部署信息 (请保存以下信息):");
  console.log("=".repeat(50));
  console.log(JSON.stringify(deploymentInfo, null, 2));
  console.log("=".repeat(50));

  console.log("\n🔗 区块链浏览器链接:");
  if (network.chainId === 11155111n) { // Sepolia
    console.log(`https://sepolia.etherscan.io/address/${zenTokenAddress}`);
  }

  console.log("\n✨ 部署完成!");
  console.log("\n📝 下一步:");
  console.log("1. 在 Etherscan 上验证合约");
  console.log("2. 将合约地址添加到钱包中查看代币");
  console.log("3. 在质押合约中使用此代币地址");
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error("❌ 部署失败:", error);
    process.exit(1);
  });
