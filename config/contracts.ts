// 合约地址配置文件
// 请在部署后更新相应的合约地址

export interface ContractAddresses {
  zenToken?: string;
  zenStake?: string;
  zenStakeProxy?: string;
}

// Sepolia 测试网合约地址
export const SEPOLIA_ADDRESSES: ContractAddresses = {
  zenToken: "0x409E0A3337f0f7583e63Ae6af186d30Aac0d4627",
  zenStake: "0xb3E14926cdC045A8E7769053669189173cF3292a",
  zenStakeProxy: "", // 如果使用代理合约，填入代理地址
};

// 本地网络合约地址
export const LOCALHOST_ADDRESSES: ContractAddresses = {
  zenToken: "",
  zenStake: "",
  zenStakeProxy: "",
};

// 主网合约地址 (生产环境)
export const MAINNET_ADDRESSES: ContractAddresses = {
  zenToken: "",
  zenStake: "",
  zenStakeProxy: "",
};

// 根据网络获取合约地址
export function getContractAddresses(networkName: string): ContractAddresses {
  switch (networkName.toLowerCase()) {
    case "sepolia":
      return SEPOLIA_ADDRESSES;
    case "localhost":
    case "hardhat":
      return LOCALHOST_ADDRESSES;
    case "mainnet":
      return MAINNET_ADDRESSES;
    default:
      throw new Error(`Unsupported network: ${networkName}`);
  }
}

// 验证地址是否有效
export function isValidAddress(address: string): boolean {
  return /^0x[a-fA-F0-9]{40}$/.test(address);
}

// 打印合约地址信息
export function printContractAddresses(addresses: ContractAddresses, networkName: string) {
  console.log(`\n📋 ${networkName} 网络合约地址:`);
  console.log("=".repeat(40));
  
  if (addresses.zenToken) {
    console.log(`ZEN Token: ${addresses.zenToken}`);
  } else {
    console.log("ZEN Token: 未部署");
  }
  
  if (addresses.zenStake) {
    console.log(`ZenStake Implementation: ${addresses.zenStake}`);
  } else {
    console.log("ZenStake Implementation: 未部署");
  }
  
  if (addresses.zenStakeProxy) {
    console.log(`ZenStake Proxy: ${addresses.zenStakeProxy}`);
  } else {
    console.log("ZenStake Proxy: 未部署");
  }
  
  console.log("=".repeat(40));
}
