import { buildModule } from "@nomicfoundation/hardhat-ignition/modules";

const ZenStakeModule = buildModule("ZenStakeModule", (m) => {
  // 部署 MockERC20 作为奖励代币 (用于测试)
  const zenToken = m.contract("MockERC20", ["ZEN Token", "ZEN", 18]);

  // 部署参数
  const startBlock = m.getParameter("startBlock", 0); // 将在脚本中设置
  const endBlock = m.getParameter("endBlock", 0);     // 将在脚本中设置
  const zenPerBlock = m.getParameter("zenPerBlock", "1000000000000000000"); // 1 ZEN per block

  // 部署可升级的 ZenStake 合约
  const zenStake = m.contract("ZenStakeImpl", [], {
    id: "ZenStakeProxy",
  });

  return { zenToken, zenStake, startBlock, endBlock, zenPerBlock };
});

export default ZenStakeModule;
